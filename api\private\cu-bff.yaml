openapi: 3.0.3
info:
  title: 'BFF Api'
  description: |
    This api is a private api that won't be published to the api registry
    Its purpose is to a BFF for the CU application
  version: 1.0.0
paths:
  /api/requests/{requestId}:
    $ref: './cu-config.yaml#/paths/~1api~1requests~1{requestId}'
  /api/requests/{requestId}/{taskCode}/close:
    $ref: './cu-config.yaml#/paths/~1api~1requests~1{requestId}~1{taskCode}~1close'
  /api/requests/{requestId}/{taskCode}/reopen:
    $ref: './cu-config.yaml#/paths/~1api~1requests~1{requestId}~1{taskCode}~1reopen'
  /api/requests/{requestId}/citizen-information:
    $ref: './cu-config.yaml#/paths/~1api~1requests~1{requestId}~1citizen-information'
  /api/requests/{requestId}/mode-of-payment:
    $ref: './cu-config.yaml#/paths/~1api~1requests~1{requestId}~1mode-of-payment'
  /api/requests/{requestId}/union-contribution:
    $ref: './cu-config.yaml#/paths/~1api~1requests~1{requestId}~1union-contribution'
  /api/requests/{requestId}/citizen-information/select:
    $ref: './cu-config.yaml#/paths/~1api~1requests~1{requestId}~1citizen-information~1select'
  /api/requests/{requestId}/mode-of-payment/select:
    $ref: './cu-config.yaml#/paths/~1api~1requests~1{requestId}~1mode-of-payment~1select'
  /api/requests/{requestId}/union-contribution/select:
    $ref: './cu-config.yaml#/paths/~1api~1requests~1{requestId}~1union-contribution~1select'
  /api/requests/{requestId}/routing-decision:
    $ref: './cu-config.yaml#/paths/~1api~1requests~1{requestId}~1routing-decision'
  /config/keycloak:
    get:
      tags:
        - Config
      operationId: getKeycloakConfig
      responses:
        '200':
          description: Configuration of Keycloak
          content:
            application/json:
              schema:
                $ref: './model.yaml#/components/schemas/KeycloakConfigResponse'

  /api/aggregate-requests/change-personal-data/CHANGE_PERSONAL_DATA_CAPTURE/{requestId}:
    get:
      tags:
        - Aggregate Request Information
      operationId: getAggregatedChangePersonalDataCaptureRequest
      description: Retrieve aggregated information about a Change of Personal Data request
      parameters:
        - name: requestId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The UUID of the request
      responses:
        '200':
          description: Basic request information successfully retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AggregatedChangePersonalDataCaptureResponse'
        '404':
          description: Request not found

  /api/aggregate-requests/change-personal-data/VALIDATION_DATA/{requestId}:
    get:
      tags:
        - Aggregate Request Information
      operationId: getAggregatedChangePersonalDataValidateRequest
      description: Retrieve aggregated information about a Change of Personal Data request
      parameters:
        - name: requestId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The UUID of the request
      responses:
        '200':
          description: Basic request information successfully retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AggregatedChangePersonalDataValidateResponse'
        '404':
          description: Request not found

  /api/aggregate-requests/change-personal-data/{requestId}:
    put:
      tags:
        - Aggregate Request Information
      operationId: updateAggregatedChangePersonalDataRequest
      description: Update aggregated information about a Change of Personal Data request
      parameters:
        - name: requestId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The UUID of the request
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateAggregatedChangePersonalDataRequest'
      responses:
        '204':
          description: No content
        '404':
          description: Request not found
  /api/lookup/country:
    get:
      operationId: searchCountry
      tags:
        - Lookup
      parameters:
        - in: query
          name: searchQuery
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CountryResponse'
  /api/lookup/city:
    get:
      operationId: searchCity
      tags:
        - Lookup
      parameters:
        - in: query
          name: searchQuery
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CityResponse'
  /api/lookup/nationality:
    get:
      operationId: searchNationality
      tags:
        - Lookup
      parameters:
        - in: query
          name: searchQuery
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/NationalityResponse'

  /api/request/c51/{requestId}:
    get:
      tags:
        - Redirect
      operationId: getC51Redirect
      description: Get a redirect URL to the C51 application
      parameters:
        - name: requestId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The UUID of the request
      responses:
        '200':
          description: Request information successfully retrieved
          content:
            text/plain:
              schema:
                type: string
        '404':
          description: Request not found

  /api/request/s24/{requestId}:
    put:
      tags:
        - Redirect
      operationId: openS24Session
      description: Opens a session to the mainframe when a T27 session is open
      parameters:
        - name: requestId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The UUID of the request
      responses:
        '200':
          description: No content returned on success
        '404':
          description: Request not found or failed to open S24 session
  /api/request/regis/{requestId}:
    get:
      tags:
        - Redirect
      operationId: getRegisRedirect
      description: Get a redirect URL to the Regis application
      parameters:
        - name: requestId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The UUID of the request
        - name: languageCode
          in: query
          required: true
          schema:
            type: string
            enum: ["fr", "nl"]
          description: The language code (fr for French, nl for Dutch)
      responses:
        '200':
          description: Regis URL successfully generated
          content:
            text/plain:
              schema:
                type: string
        '404':
          description: Request not found or failed to generate Regis URL

components:
  schemas:
    RequestInformationResponse:
      $ref: './model.yaml#/components/schemas/RequestInformationResponse'
    UpdateRequestInformationRequest:
      $ref: './model.yaml#/components/schemas/UpdateRequestInformationRequest'
    LookupFields:
      type: object
      required:
        - code
        - descFr
        - descNl
      properties:
        code:
          type: string
        descFr:
          type: string
        descNl:
          type: string
    CountryResponse:
      allOf:
        - $ref: '#/components/schemas/LookupFields'
    CityResponse:
      allOf:
        - $ref: '#/components/schemas/LookupFields'
      type: object
      required:
        - nisCode
      properties:
        nisCode:
          type: string

    NationalityResponse:
      allOf:
        - $ref: '#/components/schemas/LookupFields'

    AggregatedChangePersonalDataCaptureResponse:
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        basicInfo:
          $ref: './model.yaml#/components/schemas/RequestBasicInfoResponse'
        citizenInformation:
          $ref: './model.yaml#/components/schemas/CitizenInformationDetailNullableResponse'
        modeOfPayment:
          $ref: './model.yaml#/components/schemas/ModeOfPaymentDetailResponse'
        unionContribution:
          $ref: './model.yaml#/components/schemas/UnionContributionDetailResponse'

    AggregatedChangePersonalDataValidateResponse:
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        basicInfo:
          $ref: './model.yaml#/components/schemas/RequestBasicInfoResponse'
        citizenInformation:
          $ref: './model.yaml#/components/schemas/CitizenInformationDetailNullableResponse'
        modeOfPayment:
          $ref: './model.yaml#/components/schemas/ModeOfPaymentDetailResponse'
        unionContribution:
          $ref: './model.yaml#/components/schemas/UnionContributionDetailResponse'
        onemCitizenInformation:
          $ref: './model.yaml#/components/schemas/HistoricalCitizenOnemResponse'
        c1CitizenInformation:
          $ref: './model.yaml#/components/schemas/HistoricalCitizenC1Response'
        authenticCitizenInformation:
          $ref: './model.yaml#/components/schemas/HistoricalCitizenAuthenticSourcesResponse'
        barema:
          $ref: './model.yaml#/components/schemas/HistoricalBaremaResponse'

    UpdateAggregatedChangePersonalDataRequest:
      type: object
      properties:
        basicInfo:
          $ref: "#/components/schemas/UpdateBasicInfoRequest"
        citizenInformation:
          $ref: './model.yaml#/components/schemas/UpdateCitizenInformationRequest'
        modeOfPayment:
          $ref: './model.yaml#/components/schemas/UpdateModeOfPaymentRequest'
        unionContribution:
          $ref: './model.yaml#/components/schemas/UpdateUnionContributionRequest'

    UpdateBasicInfoRequest:
      type: object
      properties:
        requestDate:
          type: string
          format: date
          description: The date when the request was submitted

