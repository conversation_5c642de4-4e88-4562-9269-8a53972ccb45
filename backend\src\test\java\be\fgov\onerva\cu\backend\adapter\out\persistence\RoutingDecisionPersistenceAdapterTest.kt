package be.fgov.onerva.cu.backend.adapter.out.persistence

import java.time.LocalDate
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.data.repository.findByIdOrNull
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ChangePersonalDataRequestEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.RoutingDecisionItemEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.ChangePersonalDataRepository
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.RoutingDecisionItemRepository
import be.fgov.onerva.cu.backend.application.domain.IdentityDocumentType
import be.fgov.onerva.cu.backend.application.domain.IntroductionType
import be.fgov.onerva.cu.backend.application.domain.ManualVerificationType
import be.fgov.onerva.cu.backend.application.domain.RequestStatus
import be.fgov.onerva.cu.backend.application.domain.RoutingDecisionItem
import be.fgov.onerva.cu.backend.application.exception.RequestIdNotFoundException
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class RoutingDecisionPersistenceAdapterTest {

    @MockK
    lateinit var routingDecisionItemRepository: RoutingDecisionItemRepository

    @MockK
    lateinit var changePersonalDataRepository: ChangePersonalDataRepository

    @InjectMockKs
    lateinit var routingDecisionPersistenceAdapter: RoutingDecisionPersistenceAdapter

    private val requestId = UUID.randomUUID()

    private fun createRequestEntity(): ChangePersonalDataRequestEntity {
        return ChangePersonalDataRequestEntity(
            documentType = IdentityDocumentType.PAPER,
            status = RequestStatus.OPEN,
            processInWave = false,
            routingDecisions = mutableSetOf(),
            c9Id = 12345L,
            c9Type = "400",
            opKey = "OP123",
            sectOp = "SO123",
            requestDate = LocalDate.now(),
            ssin = "*********",
            unemploymentOffice = 123,
            paymentInstitution = 1,
            introductionDate = LocalDate.now(),
            dateValid = LocalDate.now().plusDays(20),
            scanNumber = 12345L,
            scanUrl = "http://example.com/scan1",
            operatorCode = 123,
            entityCode = "123456",
            introductionType = IntroductionType.INTRO_FIRST_DEMAND,
            dueDate = LocalDate.now().plusDays(12),
            numbox = 12,
            ec1Id = 1234,
            ec1DisplayUrl = "http://example.com/ec1"
        )
    }

    private fun createRoutingDecisionEntity(
        request: ChangePersonalDataRequestEntity,
        type: ManualVerificationType,
        value: Boolean?,
    ): RoutingDecisionItemEntity {
        return RoutingDecisionItemEntity(
            request = request,
            type = type,
            value = value
        )
    }

    @Test
    fun `should return routing decisions for existing request`() {
        // Given
        val routingDecisionEntities = listOf(
            createRoutingDecisionEntity(createRequestEntity(), ManualVerificationType.CITIZEN_OVER_65_YEARS_OLD, false),
            createRoutingDecisionEntity(createRequestEntity(), ManualVerificationType.NON_BELGIAN_RESIDENT, null)
        )

        every { routingDecisionItemRepository.findByRequestId(requestId) } returns routingDecisionEntities

        // When
        val result = routingDecisionPersistenceAdapter.getRoutingDecisions(requestId)

        // Then
        assertThat(result).hasSize(2)

        val citizenDecision = result.find { it.type == ManualVerificationType.CITIZEN_OVER_65_YEARS_OLD }
        assertThat(citizenDecision?.value).isEqualTo(false)

        val residentDecision = result.find { it.type == ManualVerificationType.NON_BELGIAN_RESIDENT }
        assertThat(residentDecision?.value).isNull()

        verify(exactly = 1) { routingDecisionItemRepository.findByRequestId(requestId) }
    }

    @Test
    fun `should return empty set when no routing decisions exist`() {
        // Given
        every { routingDecisionItemRepository.findByRequestId(requestId) } returns emptyList()

        // When
        val result = routingDecisionPersistenceAdapter.getRoutingDecisions(requestId)

        // Then
        assertThat(result).isEmpty()
        verify(exactly = 1) { routingDecisionItemRepository.findByRequestId(requestId) }
    }

    @Test
    fun `should save routing decisions successfully`() {
        // Given
        val requestEntity = createRequestEntity()
        val routingDecisionItems = setOf(
            RoutingDecisionItem(ManualVerificationType.CITIZEN_OVER_65_YEARS_OLD, false),
            RoutingDecisionItem(ManualVerificationType.NON_BELGIAN_RESIDENT, true),
            RoutingDecisionItem(ManualVerificationType.RELEVANT_TO_PORT_WORKER, null)
        )

        every { changePersonalDataRepository.findByIdOrNull(requestId) } returns requestEntity
        every { routingDecisionItemRepository.deleteByRequestId(requestId) } returns Unit
        every { routingDecisionItemRepository.saveAll(any<List<RoutingDecisionItemEntity>>()) } returns emptyList()

        // When
        routingDecisionPersistenceAdapter.saveRoutingDecisions(requestId, routingDecisionItems)

        // Then
        verify(exactly = 1) { changePersonalDataRepository.findByIdOrNull(requestId) }
        verify(exactly = 1) { routingDecisionItemRepository.deleteByRequestId(requestId) }
        verify(exactly = 1) { routingDecisionItemRepository.saveAll(any<List<RoutingDecisionItemEntity>>()) }
    }

    @Test
    fun `should throw exception when request not found during save`() {
        // Given
        val routingDecisionItems = setOf(
            RoutingDecisionItem(ManualVerificationType.CITIZEN_OVER_65_YEARS_OLD, false)
        )

        every { changePersonalDataRepository.findByIdOrNull(requestId) } returns null

        // When/Then
        assertThatThrownBy {
            routingDecisionPersistenceAdapter.saveRoutingDecisions(requestId, routingDecisionItems)
        }
            .isInstanceOf(RequestIdNotFoundException::class.java)
            .hasMessageContaining("Request ID not found: $requestId")

        verify(exactly = 1) { changePersonalDataRepository.findByIdOrNull(requestId) }
        verify(exactly = 0) { routingDecisionItemRepository.deleteByRequestId(any()) }
        verify(exactly = 0) { routingDecisionItemRepository.saveAll(any<List<RoutingDecisionItemEntity>>()) }
    }

    @Test
    fun `should handle saving routing decisions with null values`() {
        // Given
        val requestEntity = createRequestEntity()
        val routingDecisionItems = setOf(
            RoutingDecisionItem(ManualVerificationType.CITIZEN_OVER_65_YEARS_OLD, null),
            RoutingDecisionItem(ManualVerificationType.NON_BELGIAN_RESIDENT, null),
            RoutingDecisionItem(ManualVerificationType.RELEVANT_TO_PORT_WORKER, null)
        )

        every { changePersonalDataRepository.findByIdOrNull(requestId) } returns requestEntity
        every { routingDecisionItemRepository.deleteByRequestId(requestId) } returns Unit
        every { routingDecisionItemRepository.saveAll(any<List<RoutingDecisionItemEntity>>()) } returns emptyList()

        // When
        routingDecisionPersistenceAdapter.saveRoutingDecisions(requestId, routingDecisionItems)

        // Then
        verify(exactly = 1) { changePersonalDataRepository.findByIdOrNull(requestId) }
        verify(exactly = 1) { routingDecisionItemRepository.deleteByRequestId(requestId) }
        verify(exactly = 1) { routingDecisionItemRepository.saveAll(any<List<RoutingDecisionItemEntity>>()) }
    }

    @Test
    fun `should delete routing decisions successfully`() {
        // Given
        every { routingDecisionItemRepository.deleteByRequestId(requestId) } returns Unit

        // When
        routingDecisionPersistenceAdapter.deleteRoutingDecisions(requestId)

        // Then
        verify(exactly = 1) { routingDecisionItemRepository.deleteByRequestId(requestId) }
    }
}